<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Manage your Magic Menu account, view order history, update profile information, and track your deliveries.">
    <meta name="keywords" content="account, profile, order history, Magic Menu account, user dashboard">
    <title>My Account - Magic Menu | Manage Your Profile</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/icons/favicon.ico">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="styles/base.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/pages.css">
    <link rel="stylesheet" href="styles/responsive.css">
</head>
<body>
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Navigation -->
    <nav class="navbar" role="navigation" aria-label="Main navigation">
        <div class="container">
            <div class="navbar-container">
                <a href="index.html" class="navbar-brand" aria-label="Magic Menu Home">
                    Magic Menu
                </a>
                
                <button class="navbar-toggle" type="button" aria-expanded="false" aria-controls="navbar-nav" aria-label="Toggle navigation menu">
                    ☰
                </button>
                
                <ul class="navbar-nav" id="navbar-nav">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="menu.html">Menu</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                    <li><a href="account.html" class="active">Account</a></li>
                    <li>
                        <a href="cart.html" class="cart-link" aria-label="Shopping cart">
                            Cart (<span class="cart-count">0</span>)
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <h1>My Account</h1>
                <p>Manage your profile, view orders, and track deliveries</p>
            </div>
        </section>

        <!-- Account Content -->
        <section class="account-section section">
            <div class="container">
                <!-- Login/Register Forms (shown when not logged in) -->
                <div id="auth-forms" class="auth-forms">
                    <div class="auth-container">
                        <div class="auth-tabs">
                            <button type="button" class="auth-tab active" data-tab="login">Login</button>
                            <button type="button" class="auth-tab" data-tab="register">Register</button>
                        </div>
                        
                        <!-- Login Form -->
                        <div id="login-form" class="auth-form active">
                            <div class="form-container">
                                <h2>Welcome Back</h2>
                                <p>Sign in to your Magic Menu account</p>
                                
                                <form data-validate data-form-type="login">
                                    <div class="form-group">
                                        <label for="login-email" class="form-label">Email Address *</label>
                                        <input type="email" id="login-email" name="email" class="form-control" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="login-password" class="form-label">Password *</label>
                                        <div class="password-input">
                                            <input type="password" id="login-password" name="password" class="form-control" required>
                                            <button type="button" class="password-toggle" aria-label="Show password">👁️</button>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="remember">
                                            <span class="checkmark"></span>
                                            Remember me
                                        </label>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary btn-block">Sign In</button>
                                </form>
                                
                                <div class="auth-footer">
                                    <p><a href="#" class="forgot-password" id="forgot-password-link">Forgot your password?</a></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Register Form -->
                        <div id="register-form" class="auth-form">
                            <div class="form-container">
                                <h2>Create Account</h2>
                                <p>Join Magic Menu and start ordering authentic Nigerian cuisine</p>
                                
                                <form data-validate data-form-type="register">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="register-firstName" class="form-label">First Name *</label>
                                            <input type="text" id="register-firstName" name="firstName" class="form-control" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="register-lastName" class="form-label">Last Name *</label>
                                            <input type="text" id="register-lastName" name="lastName" class="form-control" required>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="register-email" class="form-label">Email Address *</label>
                                        <input type="email" id="register-email" name="email" class="form-control" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="register-phone" class="form-label">Phone Number *</label>
                                        <input type="tel" id="register-phone" name="phone" class="form-control" required placeholder="+234 ************">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="register-password" class="form-label">Password *</label>
                                        <div class="password-input">
                                            <input type="password" id="register-password" name="password" class="form-control" required minlength="8">
                                            <button type="button" class="password-toggle" aria-label="Show password">👁️</button>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="register-confirmPassword" class="form-label">Confirm Password *</label>
                                        <input type="password" id="register-confirmPassword" name="confirmPassword" class="form-control" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="terms" required>
                                            <span class="checkmark"></span>
                                            I agree to the <a href="terms.html" target="_blank">Terms of Service</a> and <a href="privacy.html" target="_blank">Privacy Policy</a> *
                                        </label>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="newsletter">
                                            <span class="checkmark"></span>
                                            Subscribe to our newsletter for special offers
                                        </label>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary btn-block">Create Account</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Password Reset Modal -->
                <div id="password-reset-modal" class="modal hidden">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>Reset Password</h3>
                            <button type="button" class="modal-close" id="close-reset-modal">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div id="reset-step-1">
                                <p>Enter your email address and we'll send you a link to reset your password.</p>
                                <form data-validate data-form-type="password-reset">
                                    <div class="form-group">
                                        <label for="reset-email" class="form-label">Email Address *</label>
                                        <input type="email" id="reset-email" name="email" class="form-control" required>
                                    </div>
                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-primary">Send Reset Link</button>
                                        <button type="button" class="btn btn-secondary" id="cancel-reset-btn">Cancel</button>
                                    </div>
                                </form>
                            </div>

                            <div id="reset-step-2" class="hidden">
                                <div class="success-message">
                                    <h4>Reset Link Sent!</h4>
                                    <p>We've sent a password reset link to your email address. Please check your inbox and follow the instructions to reset your password.</p>
                                    <p><small>Didn't receive the email? Check your spam folder or <a href="#" id="resend-reset-link">click here to resend</a>.</small></p>
                                    <button type="button" class="btn btn-primary" id="close-reset-success">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Dashboard (shown when logged in) -->
                <div id="account-dashboard" class="account-container hidden">
                    <!-- Account Sidebar -->
                    <div class="account-sidebar">
                        <div class="user-info">
                            <h3 id="user-name">John Doe</h3>
                            <p id="user-email"><EMAIL></p>
                            
                            <!-- Sync Status Indicator -->
                            <div class="sync-status" id="sync-status">
                                <span class="sync-indicator online" id="sync-indicator">
                                    <span class="sync-dot"></span>
                                    <span class="sync-text">Synced</span>
                                </span>
                            </div>
                        </div>
                        
                        <nav class="account-navigation">
                            <ul class="account-nav">
                                <li><a href="#profile" class="account-nav-link active" data-section="profile">Profile</a></li>
                                <li><a href="#orders" class="account-nav-link" data-section="orders">Order History</a></li>
                                <li><a href="#addresses" class="account-nav-link" data-section="addresses">Addresses</a></li>
                                <li><a href="#preferences" class="account-nav-link" data-section="preferences">Preferences</a></li>
                                <li><a href="#account-settings" class="account-nav-link" data-section="account-settings">Account Settings</a></li>
                                <li><a href="#" class="account-nav-link" id="logout-btn">Logout</a></li>
                            </ul>
                        </nav>
                    </div>
                    
                    <!-- Account Content -->
                    <div class="account-content">
                        <!-- Profile Section -->
                        <div id="profile-section" class="account-section active">
                            <h2>Profile Information</h2>

                            <!-- Profile View -->
                            <div id="profile-view">
                                <!-- Email Verification Alert -->
                                <div id="email-verification-alert" class="alert alert-warning hidden">
                                    <p><strong>Email Verification Required</strong></p>
                                    <p>Please check your email and click the verification link to activate your account.</p>
                                    <button type="button" class="btn btn-sm btn-secondary" id="resend-verification-btn">Resend Verification Email</button>
                                </div>

                                <div class="profile-info">
                                    <div class="info-item">
                                        <span class="info-label">Name:</span>
                                        <span class="info-value" id="profile-name">John Doe</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">Email:</span>
                                        <span class="info-value" id="profile-email"><EMAIL></span>
                                        <span id="email-verification-status" class="verification-status"></span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">Phone:</span>
                                        <span class="info-value" id="profile-phone">+234 ************</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">Member Since:</span>
                                        <span class="info-value" id="profile-member-since">January 2024</span>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary" id="edit-profile-btn">Edit Profile</button>
                            </div>

                            <!-- Profile Edit Form -->
                            <div id="profile-edit" class="hidden">
                                <form data-validate data-form-type="profile-update">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="edit-firstName" class="form-label">First Name *</label>
                                            <input type="text" id="edit-firstName" name="firstName" class="form-control" required>
                                        </div>

                                        <div class="form-group">
                                            <label for="edit-lastName" class="form-label">Last Name *</label>
                                            <input type="text" id="edit-lastName" name="lastName" class="form-control" required>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="edit-email" class="form-label">Email Address *</label>
                                        <input type="email" id="edit-email" name="email" class="form-control" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="edit-phone" class="form-label">Phone Number *</label>
                                        <input type="tel" id="edit-phone" name="phone" class="form-control" required placeholder="+234 ************">
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-primary">Save Changes</button>
                                        <button type="button" class="btn btn-secondary" id="cancel-edit-btn">Cancel</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Orders Section -->
                        <div id="orders-section" class="account-section">
                            <h2>Order History</h2>
                            <div id="order-history">
                                <!-- Order history will be loaded here -->
                            </div>

                            <!-- Order Details Modal -->
                            <div id="order-details-modal" class="modal hidden">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h3>Order Details</h3>
                                        <button type="button" class="modal-close" id="close-order-modal">&times;</button>
                                    </div>
                                    <div class="modal-body">
                                        <div id="order-details-content">
                                            <!-- Order details will be loaded here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Addresses Section -->
                        <div id="addresses-section" class="account-section">
                            <h2>Saved Addresses</h2>
                            <p>Manage your delivery addresses for faster checkout.</p>

                            <!-- Address List -->
                            <div id="address-list">
                                <!-- Addresses will be loaded here -->
                            </div>

                            <button type="button" class="btn btn-primary" id="add-address-btn">Add New Address</button>

                            <!-- Address Form Modal -->
                            <div id="address-modal" class="modal hidden">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h3 id="address-modal-title">Add New Address</h3>
                                        <button type="button" class="modal-close" id="close-address-modal">&times;</button>
                                    </div>
                                    <div class="modal-body">
                                        <form data-validate data-form-type="address">
                                            <div class="form-group">
                                                <label for="address-label" class="form-label">Address Label *</label>
                                                <input type="text" id="address-label" name="label" class="form-control" required placeholder="e.g., Home, Office">
                                            </div>

                                            <div class="form-group">
                                                <label for="address-street" class="form-label">Street Address *</label>
                                                <input type="text" id="address-street" name="street" class="form-control" required placeholder="123 Main Street">
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="address-city" class="form-label">City *</label>
                                                    <input type="text" id="address-city" name="city" class="form-control" required placeholder="Lagos">
                                                </div>

                                                <div class="form-group">
                                                    <label for="address-state" class="form-label">State *</label>
                                                    <select id="address-state" name="state" class="form-control" required>
                                                        <option value="">Select State</option>
                                                        <option value="Abia">Abia</option>
                                                        <option value="Adamawa">Adamawa</option>
                                                        <option value="Akwa Ibom">Akwa Ibom</option>
                                                        <option value="Anambra">Anambra</option>
                                                        <option value="Bauchi">Bauchi</option>
                                                        <option value="Bayelsa">Bayelsa</option>
                                                        <option value="Benue">Benue</option>
                                                        <option value="Borno">Borno</option>
                                                        <option value="Cross River">Cross River</option>
                                                        <option value="Delta">Delta</option>
                                                        <option value="Ebonyi">Ebonyi</option>
                                                        <option value="Edo">Edo</option>
                                                        <option value="Ekiti">Ekiti</option>
                                                        <option value="Enugu">Enugu</option>
                                                        <option value="FCT">Federal Capital Territory</option>
                                                        <option value="Gombe">Gombe</option>
                                                        <option value="Imo">Imo</option>
                                                        <option value="Jigawa">Jigawa</option>
                                                        <option value="Kaduna">Kaduna</option>
                                                        <option value="Kano">Kano</option>
                                                        <option value="Katsina">Katsina</option>
                                                        <option value="Kebbi">Kebbi</option>
                                                        <option value="Kogi">Kogi</option>
                                                        <option value="Kwara">Kwara</option>
                                                        <option value="Lagos">Lagos</option>
                                                        <option value="Nasarawa">Nasarawa</option>
                                                        <option value="Niger">Niger</option>
                                                        <option value="Ogun">Ogun</option>
                                                        <option value="Ondo">Ondo</option>
                                                        <option value="Osun">Osun</option>
                                                        <option value="Oyo">Oyo</option>
                                                        <option value="Plateau">Plateau</option>
                                                        <option value="Rivers">Rivers</option>
                                                        <option value="Sokoto">Sokoto</option>
                                                        <option value="Taraba">Taraba</option>
                                                        <option value="Yobe">Yobe</option>
                                                        <option value="Zamfara">Zamfara</option>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label for="address-phone" class="form-label">Contact Phone</label>
                                                <input type="tel" id="address-phone" name="phone" class="form-control" placeholder="+234 ************">
                                            </div>

                                            <div class="form-group">
                                                <label for="address-instructions" class="form-label">Delivery Instructions</label>
                                                <textarea id="address-instructions" name="instructions" class="form-control" rows="3" placeholder="Additional delivery instructions (optional)"></textarea>
                                            </div>

                                            <div class="form-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" name="isDefault" id="address-default">
                                                    <span class="checkmark"></span>
                                                    Set as default address
                                                </label>
                                            </div>

                                            <div class="form-actions">
                                                <button type="submit" class="btn btn-primary">Save Address</button>
                                                <button type="button" class="btn btn-secondary" id="cancel-address-btn">Cancel</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Preferences Section -->
                        <div id="preferences-section" class="account-section">
                            <h2>Notification Preferences</h2>
                            <p>Customize how and when you receive notifications from Magic Menu.</p>
                            
                            <div class="preferences-form">
                                <!-- Order Notifications -->
                                <div class="preference-group">
                                    <h3>Order Notifications</h3>
                                    <div class="preference-options">
                                        <div class="form-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="order-placed" checked>
                                                <span class="checkmark"></span>
                                                Order confirmation
                                            </label>
                                            <small class="preference-description">Get notified when your order is successfully placed</small>
                                        </div>
                                        <div class="form-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="order-preparing" checked>
                                                <span class="checkmark"></span>
                                                Order preparation updates
                                            </label>
                                            <small class="preference-description">Receive updates when your order is being prepared</small>
                                        </div>
                                        <div class="form-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="order-delivery" checked>
                                                <span class="checkmark"></span>
                                                Delivery notifications
                                            </label>
                                            <small class="preference-description">Get notified when your order is out for delivery and delivered</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Communication Channels -->
                                <div class="preference-group">
                                    <h3>Communication Channels</h3>
                                    <div class="preference-options">
                                        <div class="form-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="email-notifications" checked>
                                                <span class="checkmark"></span>
                                                Email notifications
                                            </label>
                                            <small class="preference-description">Receive notifications via email</small>
                                        </div>
                                        <div class="form-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="sms-notifications" checked>
                                                <span class="checkmark"></span>
                                                SMS notifications
                                            </label>
                                            <small class="preference-description">Receive notifications via text message</small>
                                        </div>
                                        <div class="form-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="browser-notifications">
                                                <span class="checkmark"></span>
                                                Browser notifications
                                            </label>
                                            <small class="preference-description">Show notifications in your browser</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Marketing Communications -->
                                <div class="preference-group">
                                    <h3>Marketing & Promotions</h3>
                                    <div class="preference-options">
                                        <div class="form-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="newsletter">
                                                <span class="checkmark"></span>
                                                Newsletter subscription
                                            </label>
                                            <small class="preference-description">Weekly newsletter with new menu items and special offers</small>
                                        </div>
                                        <div class="form-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="promotional-offers">
                                                <span class="checkmark"></span>
                                                Promotional offers
                                            </label>
                                            <small class="preference-description">Receive notifications about discounts and special deals</small>
                                        </div>
                                        <div class="form-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="new-menu-items">
                                                <span class="checkmark"></span>
                                                New menu items
                                            </label>
                                            <small class="preference-description">Get notified when we add new dishes to our menu</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Frequency Settings -->
                                <div class="preference-group">
                                    <h3>Notification Frequency</h3>
                                    <div class="preference-options">
                                        <div class="form-group">
                                            <label for="email-frequency" class="form-label">Email frequency</label>
                                            <select id="email-frequency" name="email-frequency" class="form-control">
                                                <option value="immediate">Immediate</option>
                                                <option value="daily" selected>Daily digest</option>
                                                <option value="weekly">Weekly summary</option>
                                                <option value="never">Never</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="marketing-frequency" class="form-label">Marketing frequency</label>
                                            <select id="marketing-frequency" name="marketing-frequency" class="form-control">
                                                <option value="weekly" selected>Weekly</option>
                                                <option value="biweekly">Bi-weekly</option>
                                                <option value="monthly">Monthly</option>
                                                <option value="never">Never</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quiet Hours -->
                                <div class="preference-group">
                                    <h3>Quiet Hours</h3>
                                    <div class="preference-options">
                                        <div class="form-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="enable-quiet-hours">
                                                <span class="checkmark"></span>
                                                Enable quiet hours
                                            </label>
                                            <small class="preference-description">Pause non-urgent notifications during specified hours</small>
                                        </div>
                                        <div class="quiet-hours-settings" id="quiet-hours-settings" style="display: none;">
                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="quiet-start" class="form-label">Start time</label>
                                                    <input type="time" id="quiet-start" name="quiet-start" class="form-control" value="22:00">
                                                </div>
                                                <div class="form-group">
                                                    <label for="quiet-end" class="form-label">End time</label>
                                                    <input type="time" id="quiet-end" name="quiet-end" class="form-control" value="08:00">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="preference-actions">
                                    <button type="button" class="btn btn-primary" id="save-preferences-btn">Save Preferences</button>
                                    <button type="button" class="btn btn-secondary" id="reset-preferences-btn">Reset to Defaults</button>
                                </div>
                                
                                <div class="preference-status" id="preference-status" style="display: none;">
                                    <span class="status-text"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Account Settings Section -->
                        <div id="account-settings-section" class="account-section">
                            <h2>Account Settings</h2>

                            <div class="settings-group">
                                <h3>Security</h3>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Change Password</h4>
                                        <p>Update your password to keep your account secure.</p>
                                    </div>
                                    <button type="button" class="btn btn-secondary" id="change-password-btn">Change Password</button>
                                </div>
                            </div>

                            <div class="settings-group">
                                <h3>Data & Privacy</h3>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Download Your Data</h4>
                                        <p>Get a copy of your account data including orders and preferences.</p>
                                    </div>
                                    <button type="button" class="btn btn-secondary" id="download-data-btn">Download Data</button>
                                </div>
                            </div>

                            <div class="settings-group danger-zone">
                                <h3>Danger Zone</h3>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Delete Account</h4>
                                        <p>Permanently delete your account and all associated data. This action cannot be undone.</p>
                                    </div>
                                    <button type="button" class="btn btn-danger" id="delete-account-btn">Delete Account</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Deletion Modal -->
                <div id="account-deletion-modal" class="modal hidden">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>Delete Account</h3>
                            <button type="button" class="modal-close" id="close-deletion-modal">&times;</button>
                        </div>
                        <div class="modal-body">
                            <!-- Step 1: Initial Confirmation -->
                            <div id="deletion-step-1">
                                <div class="deletion-warning">
                                    <div class="warning-icon">⚠️</div>
                                    <h4>Are you sure you want to delete your account?</h4>
                                    <p>This action will permanently delete:</p>
                                    <ul>
                                        <li>Your profile information</li>
                                        <li>Order history</li>
                                        <li>Saved addresses</li>
                                        <li>Preferences and settings</li>
                                        <li>All associated data</li>
                                    </ul>
                                    <p><strong>This action cannot be undone.</strong></p>
                                </div>
                                
                                <div class="deletion-options">
                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="export-data-checkbox">
                                            <span class="checkmark"></span>
                                            Download my data before deletion
                                        </label>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="button" class="btn btn-danger" id="proceed-deletion-btn">Proceed with Deletion</button>
                                    <button type="button" class="btn btn-secondary" id="cancel-deletion-btn">Cancel</button>
                                </div>
                            </div>

                            <!-- Step 2: Password Verification -->
                            <div id="deletion-step-2" class="hidden">
                                <div class="deletion-verification">
                                    <h4>Verify Your Password</h4>
                                    <p>Please enter your password to confirm account deletion.</p>
                                    
                                    <form id="deletion-password-form">
                                        <div class="form-group">
                                            <label for="deletion-password" class="form-label">Password *</label>
                                            <div class="password-input">
                                                <input type="password" id="deletion-password" name="password" class="form-control" required>
                                                <button type="button" class="password-toggle" aria-label="Show password">👁️</button>
                                            </div>
                                        </div>
                                        
                                        <div class="form-actions">
                                            <button type="submit" class="btn btn-danger">Verify Password</button>
                                            <button type="button" class="btn btn-secondary" id="back-to-step1-btn">Back</button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Step 3: Final Confirmation -->
                            <div id="deletion-step-3" class="hidden">
                                <div class="deletion-final">
                                    <div class="final-warning">
                                        <div class="warning-icon">🚨</div>
                                        <h4>Final Confirmation</h4>
                                        <p>You are about to permanently delete your Magic Menu account.</p>
                                        <p><strong>This is your last chance to cancel.</strong></p>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="final-confirmation-input" class="form-label">
                                            Type "DELETE" to confirm:
                                        </label>
                                        <input type="text" id="final-confirmation-input" class="form-control" placeholder="Type DELETE here">
                                    </div>
                                    
                                    <div class="form-actions">
                                        <button type="button" class="btn btn-danger" id="final-delete-btn" disabled>Delete My Account</button>
                                        <button type="button" class="btn btn-secondary" id="cancel-final-btn">Cancel</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 4: Deletion Complete -->
                            <div id="deletion-step-4" class="hidden">
                                <div class="deletion-complete">
                                    <div class="success-icon">✅</div>
                                    <h4>Account Deleted Successfully</h4>
                                    <p>Your Magic Menu account and all associated data have been permanently deleted.</p>
                                    <p>Thank you for using Magic Menu. We're sorry to see you go!</p>
                                    
                                    <div class="form-actions">
                                        <button type="button" class="btn btn-primary" id="deletion-complete-btn">Continue to Homepage</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Magic Menu</h3>
                    <p>Bringing authentic Nigerian cuisine to your doorstep with love, tradition, and the finest ingredients.</p>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="menu.html">Menu</a></li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="faq.html">FAQ</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Customer Service</h4>
                    <ul>
                        <li><a href="privacy.html">Privacy Policy</a></li>
                        <li><a href="terms.html">Terms of Service</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <address>
                        <p>📍 123 Victoria Island, Lagos, Nigeria</p>
                        <p>📞 <a href="tel:+2348012345678">+234 ************</a></p>
                        <p>✉️ <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    </address>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Magic Menu. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/utils.js"></script>
    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/forms.js"></script>
    <script>
        // Ensure all modals are closed on page load
        function closeAllModals() {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.classList.add('hidden');
            });
        }

        /**
         * Enhanced Page Initialization System
         * Handles robust page initialization with proper dependency checking
         */
        const PageInitializer = {
            // Initialize the page with comprehensive error handling
            init() {
                try {
                    this.log('PageInitializer starting...');
                    
                    // Initialize authentication manager first
                    this.initializeAuthentication();
                    
                    // Initialize other components
                    this.initializeComponents();
                    
                    // Setup event listeners
                    this.setupEventListeners();
                    
                    this.log('PageInitializer completed successfully');
                } catch (error) {
                    this.log('Critical error during page initialization:', error, 'error');
                    this.handleInitializationError(error);
                }
            },

            // Initialize authentication with robust error handling
            initializeAuthentication() {
                try {
                    this.log('Initializing authentication...');
                    
                    // Check if AuthenticationManager is available
                    if (typeof AuthenticationManager !== 'undefined') {
                        AuthenticationManager.init();
                        this.log('AuthenticationManager initialized successfully');
                    } else {
                        this.log('AuthenticationManager not available, using fallback', 'error');
                        this.fallbackAuthentication();
                    }
                } catch (error) {
                    this.log('Error initializing authentication:', error, 'error');
                    this.fallbackAuthentication();
                }
            },

            // Fallback authentication when AuthenticationManager fails
            fallbackAuthentication() {
                this.log('Using fallback authentication');
                
                try {
                    // Basic authentication check
                    const user = Utils && Utils.storage ? Utils.storage.get('user') : null;
                    const authForms = document.getElementById('auth-forms');
                    const dashboard = document.getElementById('account-dashboard');
                    
                    if (user && user.loggedIn && authForms && dashboard) {
                        authForms.classList.add('hidden');
                        dashboard.classList.remove('hidden');
                        this.log('Fallback: Showing dashboard for logged in user');
                    } else {
                        if (authForms) authForms.classList.remove('hidden');
                        if (dashboard) dashboard.classList.add('hidden');
                        this.log('Fallback: Showing login forms');
                    }
                } catch (error) {
                    this.log('Fallback authentication failed:', error, 'error');
                    this.emergencyFallback();
                }
            },

            // Emergency fallback when everything else fails
            emergencyFallback() {
                this.log('Using emergency fallback');
                
                try {
                    // Force show login forms using basic DOM manipulation
                    const authForms = document.querySelector('#auth-forms');
                    const dashboard = document.querySelector('#account-dashboard');
                    
                    if (authForms) {
                        authForms.style.display = 'block';
                        authForms.classList.remove('hidden');
                    }
                    
                    if (dashboard) {
                        dashboard.style.display = 'none';
                        dashboard.classList.add('hidden');
                    }
                    
                    this.log('Emergency fallback completed');
                } catch (error) {
                    this.log('Emergency fallback failed:', error, 'error');
                    console.error('Critical: All authentication fallbacks failed', error);
                }
            },

            // Initialize other page components
            initializeComponents() {
                try {
                    this.setupAuthTabs();
                    this.setupProfileEditing();
                    this.setupAddressManagement();
                    this.setupPasswordReset();
                    this.setupPreferences();
                    this.setupAccountSettings();
                    this.setupAccountDeletion();
                    this.setupCrossTabCommunication();
                    this.setupSyncStatusMonitoring();
                    
                    // Clean up expired verification tokens on page load
                    if (typeof EmailVerification !== 'undefined') {
                        EmailVerification.cleanupExpiredTokens();
                    }
                    
                    this.log('Components initialized successfully');
                } catch (error) {
                    this.log('Error initializing components:', error, 'error');
                }
            },

            // Setup event listeners
            setupEventListeners() {
                try {
                    // Close all modals on page load
                    this.closeAllModals();
                    
                    // Setup window error handler
                    window.addEventListener('error', (event) => {
                        this.log('Window error caught:', event.error, 'error');
                    });
                    
                    // Setup unhandled promise rejection handler
                    window.addEventListener('unhandledrejection', (event) => {
                        this.log('Unhandled promise rejection:', event.reason, 'error');
                    });
                    
                    this.log('Event listeners setup completed');
                } catch (error) {
                    this.log('Error setting up event listeners:', error, 'error');
                }
            },

            // Close all modals
            closeAllModals() {
                try {
                    const modals = document.querySelectorAll('.modal');
                    modals.forEach(modal => {
                        modal.classList.add('hidden');
                    });
                    this.log('All modals closed');
                } catch (error) {
                    this.log('Error closing modals:', error, 'error');
                }
            },

            // Handle initialization errors
            handleInitializationError(error) {
                this.log('Handling initialization error:', error, 'error');
                
                try {
                    // Try to show user-friendly error
                    if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                        MagicMenu.showToast('Page initialization error. Please refresh the page.', 'error', 5000);
                    }
                    
                    // Force fallback authentication
                    this.emergencyFallback();
                } catch (fallbackError) {
                    this.log('Error handler failed:', fallbackError, 'error');
                    console.error('Critical: Page initialization completely failed', error, fallbackError);
                }
            },

            // Enhanced logging
            log(message, data = null, level = 'info') {
                const timestamp = new Date().toISOString();
                const logEntry = `[${timestamp}] PageInit: ${message}`;
                
                switch (level) {
                    case 'error':
                        console.error(logEntry, data);
                        break;
                    case 'warn':
                        console.warn(logEntry, data);
                        break;
                    default:
                        console.log(logEntry, data);
                }
            }
        };

        // Setup functions for PageInitializer
        PageInitializer.setupAuthTabs = function() {
            try {
                // Auth tab switching functionality
                const authTabs = document.querySelectorAll('.auth-tab');
                const authForms = document.querySelectorAll('.auth-form');
                
                authTabs.forEach(tab => {
                    tab.addEventListener('click', () => {
                        const targetTab = tab.dataset.tab;
                        
                        // Update active tab
                        authTabs.forEach(t => t.classList.remove('active'));
                        tab.classList.add('active');
                        
                        // Show corresponding form
                        authForms.forEach(form => {
                            if (form.id === `${targetTab}-form`) {
                                form.classList.add('active');
                            } else {
                                form.classList.remove('active');
                            }
                        });
                    });
                });
                
                this.log('Auth tabs setup completed');
            } catch (error) {
                this.log('Error setting up auth tabs:', error, 'error');
            }
        };

        PageInitializer.setupProfileEditing = function() {
            try {
                // Profile editing functionality
                const editBtn = document.getElementById('edit-profile-btn');
                const cancelBtn = document.getElementById('cancel-edit-btn');
                const profileView = document.getElementById('profile-view');
                const profileEdit = document.getElementById('profile-edit');
                
                if (editBtn && profileView && profileEdit) {
                    editBtn.addEventListener('click', () => {
                        profileView.classList.add('hidden');
                        profileEdit.classList.remove('hidden');
                    });
                }
                
                if (cancelBtn && profileView && profileEdit) {
                    cancelBtn.addEventListener('click', () => {
                        profileEdit.classList.add('hidden');
                        profileView.classList.remove('hidden');
                    });
                }
                
                this.log('Profile editing setup completed');
            } catch (error) {
                this.log('Error setting up profile editing:', error, 'error');
            }
        };

        PageInitializer.setupAddressManagement = function() {
            try {
                // Address management functionality
                const addAddressBtn = document.getElementById('add-address-btn');
                const addressModal = document.getElementById('address-modal');
                const closeAddressModal = document.getElementById('close-address-modal');
                const cancelAddressBtn = document.getElementById('cancel-address-btn');
                
                if (addAddressBtn && addressModal) {
                    addAddressBtn.addEventListener('click', () => {
                        addressModal.classList.remove('hidden');
                    });
                }
                
                if (closeAddressModal && addressModal) {
                    closeAddressModal.addEventListener('click', () => {
                        addressModal.classList.add('hidden');
                    });
                }
                
                if (cancelAddressBtn && addressModal) {
                    cancelAddressBtn.addEventListener('click', () => {
                        addressModal.classList.add('hidden');
                    });
                }
                
                this.log('Address management setup completed');
            } catch (error) {
                this.log('Error setting up address management:', error, 'error');
            }
        };

        PageInitializer.setupPasswordReset = function() {
            try {
                // Password reset functionality
                const forgotPasswordLink = document.getElementById('forgot-password-link');
                const passwordResetModal = document.getElementById('password-reset-modal');
                const closeResetModal = document.getElementById('close-reset-modal');
                const cancelResetBtn = document.getElementById('cancel-reset-btn');
                
                if (forgotPasswordLink && passwordResetModal) {
                    forgotPasswordLink.addEventListener('click', (e) => {
                        e.preventDefault();
                        passwordResetModal.classList.remove('hidden');
                    });
                }
                
                if (closeResetModal && passwordResetModal) {
                    closeResetModal.addEventListener('click', () => {
                        passwordResetModal.classList.add('hidden');
                    });
                }
                
                if (cancelResetBtn && passwordResetModal) {
                    cancelResetBtn.addEventListener('click', () => {
                        passwordResetModal.classList.add('hidden');
                    });
                }
                
                this.log('Password reset setup completed');
            } catch (error) {
                this.log('Error setting up password reset:', error, 'error');
            }
        };

        PageInitializer.setupPreferences = function() {
            try {
                // Notification preferences functionality
                const preferenceCheckboxes = document.querySelectorAll('#preferences-section input[type="checkbox"]');
                
                preferenceCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', () => {
                        // Save preference immediately
                        const preferences = Utils.storage.get('userPreferences', {});
                        preferences[checkbox.name] = checkbox.checked;
                        Utils.storage.set('userPreferences', preferences);
                        
                        if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                            MagicMenu.showToast('Preference saved', 'success', 2000);
                        }
                    });
                });
                
                this.log('Preferences setup completed');
            } catch (error) {
                this.log('Error setting up preferences:', error, 'error');
            }
        };

        PageInitializer.setupAccountSettings = function() {
            try {
                // Account settings functionality
                const accountNavLinks = document.querySelectorAll('.account-nav-link');
                const accountSections = document.querySelectorAll('.account-section');
                
                accountNavLinks.forEach(link => {
                    link.addEventListener('click', (e) => {
                        if (link.id === 'logout-btn') return; // Skip logout button
                        
                        e.preventDefault();
                        const targetSection = link.dataset.section;
                        
                        // Update active nav item
                        accountNavLinks.forEach(l => l.classList.remove('active'));
                        link.classList.add('active');
                        
                        // Show corresponding section
                        accountSections.forEach(section => {
                            if (section.id === `${targetSection}-section`) {
                                section.classList.add('active');
                            } else {
                                section.classList.remove('active');
                            }
                        });
                    });
                });
                
                this.log('Account settings setup completed');
            } catch (error) {
                this.log('Error setting up account settings:', error, 'error');
            }
        };

        PageInitializer.setupAccountDeletion = function() {
            try {
                // Account deletion functionality will be implemented in later tasks
                this.log('Account deletion setup completed (placeholder)');
            } catch (error) {
                this.log('Error setting up account deletion:', error, 'error');
            }
        };

        PageInitializer.setupCrossTabCommunication = setupCrossTabCommunication;
        PageInitializer.setupSyncStatusMonitoring = setupSyncStatusMonitoring;

        // Enhanced resend verification function
        function handleResendVerification(email, button) {
            // Disable button and show loading
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = 'Sending...';
            
            const result = EmailVerification.resendVerification(email);
            
            if (result.success) {
                MagicMenu.showToast('Verification email sent! Please check your inbox.', 'success');
                
                // Start cooldown timer (60 seconds)
                let countdown = 60;
                const updateButton = () => {
                    if (countdown > 0) {
                        button.textContent = `Resend in ${countdown}s`;
                        countdown--;
                        setTimeout(updateButton, 1000);
                    } else {
                        button.disabled = false;
                        button.textContent = originalText;
                    }
                };
                updateButton();
            } else {
                MagicMenu.showToast(result.error, 'error');
                button.disabled = false;
                button.textContent = originalText;
            }
        }

        // Setup cross-tab communication for email verification and account deletion
        function setupCrossTabCommunication() {
            if (typeof BroadcastChannel !== 'undefined') {
                const channel = new BroadcastChannel('magic-menu-updates');
                
                channel.addEventListener('message', (event) => {
                    if (event.data.type === 'EMAIL_VERIFIED') {
                        // Update UI when email is verified in another tab
                        const user = Utils.storage.get('user');
                        if (user && user.email === event.data.email) {
                            user.emailVerified = true;
                            user.verifiedAt = event.data.timestamp;
                            Utils.storage.set('user', user);
                            
                            // Refresh the page to show updated status
                            window.location.reload();
                        }
                    } else if (event.data.type === 'ACCOUNT_DELETED') {
                        // Handle account deletion in other tabs
                        const user = Utils.storage.get('user');
                        if (user && user.email === event.data.email) {
                            // Show notification and redirect
                            MagicMenu.showToast('Account has been deleted in another tab', 'info');
                            setTimeout(() => {
                                window.location.href = 'index.html';
                            }, 2000);
                        }
                    }
                });
            }
        }

        // Setup sync status monitoring
        function setupSyncStatusMonitoring() {
            const syncIndicator = document.getElementById('sync-indicator');
            const syncText = syncIndicator?.querySelector('.sync-text');
            
            if (!syncIndicator || !syncText) return;

            // Update sync status
            function updateSyncStatus() {
                if (typeof DataSync === 'undefined') {
                    syncIndicator.className = 'sync-indicator offline';
                    syncText.textContent = 'Sync Unavailable';
                    return;
                }

                const status = DataSync.getSyncStatus();
                
                if (!status.isOnline) {
                    syncIndicator.className = 'sync-indicator offline';
                    syncText.textContent = 'Offline';
                } else if (status.pendingChanges > 0 || status.queuedItems > 0) {
                    syncIndicator.className = 'sync-indicator syncing';
                    syncText.textContent = 'Syncing...';
                } else {
                    syncIndicator.className = 'sync-indicator online';
                    syncText.textContent = 'Synced';
                }
            }

            // Initial status update
            updateSyncStatus();

            // Update status periodically
            setInterval(updateSyncStatus, 5000);

            // Listen for network status changes
            window.addEventListener('online', updateSyncStatus);
            window.addEventListener('offline', updateSyncStatus);

            // Listen for data sync events
            window.addEventListener('dataSync', (event) => {
                // Show brief syncing status
                syncIndicator.className = 'sync-indicator syncing';
                syncText.textContent = 'Syncing...';
                
                setTimeout(updateSyncStatus, 1000);
            });

            // Add click handler for manual sync
            syncIndicator.addEventListener('click', () => {
                if (typeof DataSync !== 'undefined') {
                    // Trigger manual sync of current user data
                    const user = Utils.storage.get('user');
                    if (user) {
                        DataSync.triggerSync('user');
                        MagicMenu.showToast('Manual sync triggered', 'info');
                    }
                }
            });
        }

        /**
         * Enhanced Authentication Manager
         * Handles robust authentication state detection and UI rendering
         */
        const AuthenticationManager = {
            // Debug logging flag
            debugMode: true,

            // Initialize authentication manager
            init() {
                this.log('AuthenticationManager initialized');
                this.checkDependencies();
                this.checkAuthStatus();
            },

            // Enhanced dependency checking with retry mechanism
            checkDependencies() {
                const dependencies = {
                    Utils: typeof Utils !== 'undefined' && Utils.storage,
                    MagicMenu: typeof MagicMenu !== 'undefined',
                    EmailVerification: typeof EmailVerification !== 'undefined'
                };

                this.log('Dependency check:', dependencies);

                // Check for critical missing dependencies
                if (!dependencies.Utils) {
                    this.log('Critical: Utils object or Utils.storage not available', 'error');
                    
                    // Try to wait a bit and retry (scripts might still be loading)
                    if (!this.dependencyRetryCount) {
                        this.dependencyRetryCount = 0;
                    }
                    
                    if (this.dependencyRetryCount < 3) {
                        this.dependencyRetryCount++;
                        this.log(`Retrying dependency check (attempt ${this.dependencyRetryCount})`, 'warn');
                        
                        setTimeout(() => {
                            this.checkAuthStatus();
                        }, 100 * this.dependencyRetryCount); // Exponential backoff
                        
                        return false;
                    } else {
                        this.log('Max dependency retries exceeded', 'error');
                        this.handleMissingDependencies(['Utils']);
                        return false;
                    }
                }

                // Reset retry count on success
                this.dependencyRetryCount = 0;
                return true;
            },

            // Enhanced authentication status checking with comprehensive error handling
            checkAuthStatus() {
                this.log('Starting authentication status check');

                try {
                    // First, ensure dependencies are available
                    if (!this.checkDependencies()) {
                        this.log('Dependencies check failed, falling back to login forms', 'error');
                        this.showLoginForms('dependency_failure');
                        return;
                    }

                    // Get DOM elements with validation
                    const elements = this.getDOMElements();
                    if (!elements.valid) {
                        this.log('Required DOM elements not found', 'error');
                        this.handleDOMElementsError();
                        return;
                    }

                    // Attempt to get user data with comprehensive error handling
                    let user = null;
                    try {
                        user = Utils.storage.get('user');
                        this.log('User data retrieved:', user ? 'Found' : 'Not found');
                        
                        // Additional validation for user data integrity
                        if (user && typeof user !== 'object') {
                            this.log('Invalid user data format detected', 'error');
                            this.handleCorruptedUserData();
                            return;
                        }
                        
                    } catch (storageError) {
                        this.log('Storage error while getting user data:', storageError, 'error');
                        this.handleStorageError(storageError);
                        return;
                    }

                    // Validate user session with enhanced checks
                    const isValidSession = this.validateUserSession(user);
                    this.log('Session validation result:', isValidSession);

                    if (isValidSession) {
                        this.log('Valid session found, showing dashboard');
                        this.showAccountDashboard(user, elements);
                    } else {
                        this.log('No valid session, showing login forms');
                        this.showLoginForms('no_valid_session');
                    }

                } catch (error) {
                    this.log('Critical error in checkAuthStatus:', error, 'error');
                    this.handleCriticalError(error);
                }
            },

            // Validate user session with comprehensive checks and error handling
            validateUserSession(user) {
                try {
                    if (!user) {
                        this.log('No user data found');
                        return false;
                    }

                    // Check if user is an object
                    if (typeof user !== 'object') {
                        this.log('User data is not an object', 'error');
                        return false;
                    }

                    // Check loggedIn status
                    if (!user.loggedIn) {
                        this.log('User not logged in');
                        return false;
                    }

                    // Check for required user properties with type validation
                    const requiredProps = ['email', 'name'];
                    for (const prop of requiredProps) {
                        if (!user[prop] || typeof user[prop] !== 'string' || user[prop].trim() === '') {
                            this.log(`Missing or invalid required user property: ${prop}`, 'error');
                            return false;
                        }
                    }

                    // Validate email format
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(user.email)) {
                        this.log('Invalid email format in user data', 'error');
                        return false;
                    }

                    // Check session expiry if available
                    if (user.sessionExpiry) {
                        try {
                            const now = new Date();
                            const expiry = new Date(user.sessionExpiry);
                            
                            // Validate expiry date
                            if (isNaN(expiry.getTime())) {
                                this.log('Invalid session expiry date format', 'error');
                                return false;
                            }
                            
                            if (now > expiry) {
                                this.log('User session expired', 'error');
                                this.handleSessionExpiry(user);
                                return false;
                            }
                        } catch (dateError) {
                            this.log('Error checking session expiry:', dateError, 'error');
                            // Continue without expiry check if date parsing fails
                        }
                    }

                    // Additional integrity checks
                    if (user.registrationTime) {
                        try {
                            const regTime = new Date(user.registrationTime);
                            if (isNaN(regTime.getTime()) || regTime > new Date()) {
                                this.log('Invalid registration time in user data', 'error');
                                return false;
                            }
                        } catch (regTimeError) {
                            this.log('Error validating registration time:', regTimeError, 'error');
                            // Continue without registration time check
                        }
                    }

                    this.log('User session validation passed');
                    return true;

                } catch (error) {
                    this.log('Error during user session validation:', error, 'error');
                    return false;
                }
            },

            // Get and validate DOM elements
            getDOMElements() {
                const authForms = document.getElementById('auth-forms');
                const accountDashboard = document.getElementById('account-dashboard');

                const elements = {
                    authForms,
                    accountDashboard,
                    valid: authForms && accountDashboard
                };

                if (!elements.valid) {
                    this.log('Missing DOM elements:', {
                        authForms: !!authForms,
                        accountDashboard: !!accountDashboard
                    }, 'error');
                }

                return elements;
            },

            // Show login forms with comprehensive error handling and reason tracking
            showLoginForms(reason = 'default') {
                this.log(`Showing login forms. Reason: ${reason}`);

                try {
                    const elements = this.getDOMElements();
                    
                    // Show auth forms with multiple fallback methods
                    if (elements.authForms) {
                        elements.authForms.classList.remove('hidden');
                        elements.authForms.style.display = 'block'; // Ensure visibility
                        this.log('Auth forms shown');
                    } else {
                        this.log('Auth forms element not found, trying alternative selectors', 'error');
                        // Try alternative selectors
                        const altAuthForms = document.querySelector('.auth-forms') || 
                                           document.querySelector('[id*="auth"]') ||
                                           document.querySelector('.login-form');
                        if (altAuthForms) {
                            altAuthForms.classList.remove('hidden');
                            altAuthForms.style.display = 'block';
                            this.log('Auth forms shown using alternative selector');
                        }
                    }

                    // Hide dashboard with multiple fallback methods
                    if (elements.accountDashboard) {
                        elements.accountDashboard.classList.add('hidden');
                        elements.accountDashboard.style.display = 'none'; // Ensure hidden
                        this.log('Account dashboard hidden');
                    } else {
                        this.log('Account dashboard element not found, trying alternative selectors', 'error');
                        // Try alternative selectors
                        const altDashboard = document.querySelector('.account-container') ||
                                           document.querySelector('[id*="dashboard"]') ||
                                           document.querySelector('.account-content');
                        if (altDashboard) {
                            altDashboard.classList.add('hidden');
                            altDashboard.style.display = 'none';
                            this.log('Account dashboard hidden using alternative selector');
                        }
                    }

                    // Track fallback reason for debugging
                    this.trackFallbackReason(reason);

                    // Clear any user-related UI state
                    this.clearUserUIState();

                } catch (error) {
                    this.log('Error showing login forms:', error, 'error');
                    // Last resort: try to show forms using basic DOM manipulation
                    this.emergencyShowLoginForms();
                }
            },

            // Show account dashboard with enhanced error handling
            showAccountDashboard(user, elements) {
                this.log('Showing account dashboard for user:', user.email);

                try {
                    elements.authForms.classList.add('hidden');
                    elements.accountDashboard.classList.remove('hidden');
                    
                    // Load user data with error handling
                    this.loadUserDataSafely(user);
                    
                    this.log('Account dashboard displayed successfully');

                } catch (error) {
                    this.log('Error showing account dashboard:', error, 'error');
                    // Fallback to login forms if dashboard fails
                    this.showLoginForms('dashboard_error');
                    this.showUserFriendlyError('Failed to load account dashboard. Please try logging in again.');
                }
            },

            // Safely load user data with comprehensive error handling
            loadUserDataSafely(user) {
                try {
                    if (typeof loadUserData === 'function') {
                        loadUserData(user);
                        this.log('User data loaded successfully');
                    } else {
                        this.log('loadUserData function not available', 'error');
                        this.loadUserDataFallback(user);
                    }
                } catch (error) {
                    this.log('Error loading user data:', error, 'error');
                    this.loadUserDataFallback(user);
                }
            },

            // Fallback user data loading
            loadUserDataFallback(user) {
                this.log('Using fallback user data loading');
                
                try {
                    // Basic user info display
                    const userNameEl = document.getElementById('user-name');
                    const userEmailEl = document.getElementById('user-email');
                    
                    if (userNameEl) userNameEl.textContent = user.name || 'User';
                    if (userEmailEl) userEmailEl.textContent = user.email || '';
                    
                    this.log('Fallback user data loaded');
                } catch (error) {
                    this.log('Fallback user data loading failed:', error, 'error');
                }
            },

            // Handle missing dependencies with comprehensive fallback
            handleMissingDependencies(missing) {
                this.log(`Missing dependencies: ${missing.join(', ')}`, 'error');
                
                // Try to create minimal Utils fallback if missing
                if (missing.includes('Utils')) {
                    this.createUtilsFallback();
                }
                
                // Show login forms as safe fallback
                this.emergencyShowLoginForms();
                
                // Show user-friendly error
                this.showUserFriendlyError(
                    'Some required components failed to load. Please refresh the page.'
                );
            },

            // Create minimal Utils fallback for basic functionality
            createUtilsFallback() {
                this.log('Creating Utils fallback');
                
                try {
                    if (typeof Utils === 'undefined') {
                        window.Utils = {
                            storage: {
                                get: function(key, defaultValue = null) {
                                    try {
                                        const item = localStorage.getItem(key);
                                        return item ? JSON.parse(item) : defaultValue;
                                    } catch (error) {
                                        console.error('Fallback storage get error:', error);
                                        return defaultValue;
                                    }
                                },
                                set: function(key, value) {
                                    try {
                                        localStorage.setItem(key, JSON.stringify(value));
                                        return true;
                                    } catch (error) {
                                        console.error('Fallback storage set error:', error);
                                        return false;
                                    }
                                },
                                remove: function(key) {
                                    try {
                                        localStorage.removeItem(key);
                                        return true;
                                    } catch (error) {
                                        console.error('Fallback storage remove error:', error);
                                        return false;
                                    }
                                }
                            }
                        };
                        this.log('Utils fallback created successfully');
                    }
                } catch (error) {
                    this.log('Failed to create Utils fallback:', error, 'error');
                }
            },

            // Handle DOM elements error
            handleDOMElementsError() {
                this.log('Critical DOM elements missing', 'error');
                
                // Try to create basic structure if possible
                this.createBasicAuthStructure();
                
                this.showUserFriendlyError(
                    'Page structure error detected. Please refresh the page.'
                );
            },

            // Handle storage errors
            handleStorageError(error) {
                this.log('Storage error:', error, 'error');
                
                // Show login forms as safe fallback
                this.showLoginForms('storage_error');
                
                // Check storage availability and show appropriate message
                try {
                    if (Utils && Utils.storage && !Utils.storage.isStorageAvailable()) {
                        this.showUserFriendlyError(
                            'Browser storage is not available. Some features may not work properly.'
                        );
                    } else {
                        this.showUserFriendlyError(
                            'Error accessing stored data. Please try logging in again.'
                        );
                    }
                } catch (checkError) {
                    this.log('Error checking storage availability:', checkError, 'error');
                    this.showUserFriendlyError(
                        'Storage system error. Please refresh the page.'
                    );
                }
            },

            // Handle corrupted user data
            handleCorruptedUserData() {
                this.log('Handling corrupted user data', 'error');
                
                try {
                    // Clear corrupted data
                    Utils.storage.remove('user');
                    this.log('Corrupted user data cleared');
                } catch (error) {
                    this.log('Failed to clear corrupted user data:', error, 'error');
                }
                
                // Show login forms
                this.showLoginForms('corrupted_data');
                
                // Notify user
                this.showUserFriendlyError(
                    'Account data was corrupted and has been cleared. Please log in again.'
                );
            },

            // Handle session expiry
            handleSessionExpiry(user) {
                this.log(`Session expired for user: ${user.email}`, 'error');
                
                // Clear expired session
                try {
                    Utils.storage.remove('user');
                    this.log('Expired session cleared');
                } catch (error) {
                    this.log('Error clearing expired session:', error, 'error');
                }
                
                // Show login forms
                this.showLoginForms('session_expired');
                
                // Notify user
                this.showUserFriendlyError(
                    'Your session has expired. Please log in again.'
                );
            },

            // Handle critical errors
            handleCriticalError(error) {
                this.log('Critical error in authentication:', error, 'error');
                
                // Emergency fallback
                this.emergencyShowLoginForms();
                
                // Show error to user
                this.showUserFriendlyError(
                    'An unexpected error occurred. Please refresh the page and try again.'
                );
            },

            // Clear user-related UI state
            clearUserUIState() {
                try {
                    // Clear user name and email displays
                    const userNameElements = document.querySelectorAll('#user-name, #profile-name');
                    const userEmailElements = document.querySelectorAll('#user-email, #profile-email');
                    
                    userNameElements.forEach(el => {
                        if (el) el.textContent = '';
                    });
                    
                    userEmailElements.forEach(el => {
                        if (el) el.textContent = '';
                    });
                    
                    // Hide verification alerts
                    const verificationAlert = document.getElementById('email-verification-alert');
                    if (verificationAlert) {
                        verificationAlert.classList.add('hidden');
                    }
                    
                    this.log('User UI state cleared');
                } catch (error) {
                    this.log('Error clearing user UI state:', error, 'error');
                }
            },

            // Emergency login forms display (last resort with comprehensive fallbacks)
            emergencyShowLoginForms() {
                this.log('Emergency login forms display activated');
                
                try {
                    // Multiple fallback strategies
                    const strategies = [
                        // Strategy 1: Standard selectors
                        () => {
                            const authForms = document.querySelector('#auth-forms');
                            const dashboard = document.querySelector('#account-dashboard');
                            
                            if (authForms) {
                                authForms.style.display = 'block';
                                authForms.style.visibility = 'visible';
                                authForms.classList.remove('hidden');
                            }
                            
                            if (dashboard) {
                                dashboard.style.display = 'none';
                                dashboard.style.visibility = 'hidden';
                                dashboard.classList.add('hidden');
                            }
                            
                            return authForms !== null;
                        },
                        
                        // Strategy 2: Class-based selectors
                        () => {
                            const authForms = document.querySelector('.auth-forms');
                            const dashboard = document.querySelector('.account-container');
                            
                            if (authForms) {
                                authForms.style.display = 'block';
                                authForms.classList.remove('hidden');
                            }
                            
                            if (dashboard) {
                                dashboard.style.display = 'none';
                                dashboard.classList.add('hidden');
                            }
                            
                            return authForms !== null;
                        },
                        
                        // Strategy 3: Create basic login form if none exists
                        () => {
                            const container = document.querySelector('.account-section .container') ||
                                            document.querySelector('.container') ||
                                            document.body;
                            
                            if (container) {
                                const loginDiv = document.createElement('div');
                                loginDiv.id = 'emergency-auth-forms';
                                loginDiv.innerHTML = `
                                    <div style="text-align: center; padding: 20px; border: 1px solid #ddd; margin: 20px;">
                                        <h2>Account Access</h2>
                                        <p>Please refresh the page to access the login form.</p>
                                        <button onclick="window.location.reload()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                            Refresh Page
                                        </button>
                                    </div>
                                `;
                                
                                // Hide any existing dashboard
                                const existingDashboard = document.querySelector('#account-dashboard');
                                if (existingDashboard) {
                                    existingDashboard.style.display = 'none';
                                }
                                
                                container.appendChild(loginDiv);
                                return true;
                            }
                            
                            return false;
                        }
                    ];
                    
                    // Try each strategy until one succeeds
                    for (let i = 0; i < strategies.length; i++) {
                        try {
                            if (strategies[i]()) {
                                this.log(`Emergency display strategy ${i + 1} succeeded`);
                                return;
                            }
                        } catch (strategyError) {
                            this.log(`Emergency display strategy ${i + 1} failed:`, strategyError, 'error');
                        }
                    }
                    
                    this.log('All emergency display strategies failed', 'error');
                    
                } catch (error) {
                    this.log('Emergency display completely failed:', error, 'error');
                    // Final fallback: show alert
                    try {
                        alert('Page initialization failed. Please refresh the page.');
                    } catch (alertError) {
                        console.error('Critical: All fallback mechanisms failed', error, alertError);
                    }
                }
            },

            // Create basic auth structure if missing
            createBasicAuthStructure() {
                this.log('Attempting to create basic auth structure');
                
                try {
                    const container = document.querySelector('.account-section .container');
                    if (container && !document.getElementById('auth-forms')) {
                        const authDiv = document.createElement('div');
                        authDiv.id = 'auth-forms';
                        authDiv.innerHTML = '<p>Please refresh the page to access the login form.</p>';
                        container.appendChild(authDiv);
                        this.log('Basic auth structure created');
                    }
                } catch (error) {
                    this.log('Failed to create basic auth structure:', error, 'error');
                }
            },

            // Track fallback reasons for debugging
            trackFallbackReason(reason) {
                try {
                    const fallbackStats = Utils.storage.get('authFallbackStats', {});
                    fallbackStats[reason] = (fallbackStats[reason] || 0) + 1;
                    fallbackStats.lastFallback = new Date().toISOString();
                    Utils.storage.set('authFallbackStats', fallbackStats, { skipSync: true });
                    this.log('Fallback reason tracked:', reason);
                } catch (error) {
                    this.log('Failed to track fallback reason:', error, 'error');
                }
            },

            // Show user-friendly error messages
            showUserFriendlyError(message) {
                try {
                    if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                        MagicMenu.showToast(message, 'error', 5000);
                        this.log('User error message shown:', message);
                    } else {
                        // Fallback to alert if toast not available
                        console.error('User Error:', message);
                        this.log('Toast not available, error logged to console');
                    }
                } catch (error) {
                    this.log('Failed to show user error:', error, 'error');
                }
            },

            // Enhanced logging with levels
            log(message, data = null, level = 'info') {
                if (!this.debugMode && level === 'info') return;
                
                const timestamp = new Date().toISOString();
                const logEntry = `[${timestamp}] AuthManager: ${message}`;
                
                switch (level) {
                    case 'error':
                        console.error(logEntry, data);
                        break;
                    case 'warn':
                        console.warn(logEntry, data);
                        break;
                    default:
                        console.log(logEntry, data);
                }
            },

            // Get authentication status for external use
            getAuthStatus() {
                try {
                    if (!this.checkDependencies()) {
                        return { authenticated: false, reason: 'dependencies_missing' };
                    }
                    
                    const user = Utils.storage.get('user');
                    const isValid = this.validateUserSession(user);
                    
                    return {
                        authenticated: isValid,
                        user: isValid ? user : null,
                        reason: isValid ? 'valid_session' : 'invalid_session'
                    };
                } catch (error) {
                    this.log('Error getting auth status:', error, 'error');
                    return { authenticated: false, reason: 'error', error: error.message };
                }
            }
        };

        // Make checkAuthStatus globally accessible (backward compatibility)
        window.checkAuthStatus = function checkAuthStatus() {
            AuthenticationManager.checkAuthStatus();
        };

        // Make AuthenticationManager globally accessible
        window.AuthenticationManager = AuthenticationManager;

        function loadUserData(user) {
            document.getElementById('user-name').textContent = user.name;
            document.getElementById('user-email').textContent = user.email;
            document.getElementById('profile-name').textContent = user.name;
            document.getElementById('profile-email').textContent = user.email;
            document.getElementById('profile-phone').textContent = user.phone || 'Not provided';

            // Handle email verification status
            const verificationStatus = document.getElementById('email-verification-status');
            const verificationAlert = document.getElementById('email-verification-alert');

            if (user.emailVerified === false) {
                verificationStatus.innerHTML = '<span class="badge badge-warning">Unverified</span>';
                verificationAlert.classList.remove('hidden');

                // Setup resend verification button with enhanced functionality
                const resendBtn = document.getElementById('resend-verification-btn');
                if (resendBtn) {
                    resendBtn.addEventListener('click', () => {
                        handleResendVerification(user.email, resendBtn);
                    });
                }
            } else {
                verificationStatus.innerHTML = '<span class="badge badge-success">Verified</span>';
                verificationAlert.classList.add('hidden');
                
                // Show verification date if available
                if (user.verifiedAt) {
                    const verifiedDate = Utils.formatDate(user.verifiedAt);
                    verificationStatus.innerHTML += `<small class="verification-date"> (Verified ${verifiedDate})</small>`;
                }
            }

            if (user.registrationTime) {
                const memberSince = Utils.formatDate(user.registrationTime, { year: 'numeric', month: 'long' });
                document.getElementById('profile-member-since').textContent = memberSince;
            }

            loadOrderHistory();
            loadAddresses();
            loadPreferences();
        }

        function loadOrderHistory() {
            const orderHistoryContainer = document.getElementById('order-history');
            
            // Get all orders using the new OrderTracking system
            const orders = OrderTracking.getUserOrders();
            
            if (orders.length > 0) {
                orderHistoryContainer.innerHTML = orders.map(order => {
                    const statusInfo = OrderTracking.ORDER_STATUSES.find(s => s.key === order.status);
                    const deliveryEstimate = OrderTracking.getDeliveryEstimate(order);
                    const progress = OrderTracking.getStatusProgress(order.status);
                    
                    return `
                        <div class="order-history-item">
                            <div class="order-header">
                                <div class="order-id-section">
                                    <span class="order-id">#${order.orderId}</span>
                                    <span class="tracking-number">Tracking: ${order.trackingNumber}</span>
                                </div>
                                <span class="order-status ${order.status}">${statusInfo?.label || order.status}</span>
                            </div>
                            
                            <div class="order-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${progress}%"></div>
                                </div>
                                <div class="progress-text">${statusInfo?.description || ''}</div>
                            </div>
                            
                            <div class="order-details">
                                <p><strong>Date:</strong> ${Utils.formatDate(order.createdAt)}</p>
                                <p><strong>Total:</strong> ${Utils.formatCurrency(order.totals.total)}</p>
                                <p><strong>Items:</strong> ${order.items.length} item(s)</p>
                                ${deliveryEstimate ? `
                                    <p><strong>${deliveryEstimate.type === 'delivered' ? 'Delivered:' : 'Estimated Delivery:'}:</strong> 
                                    ${deliveryEstimate.formatted}
                                    ${deliveryEstimate.minutesRemaining > 0 ? ` (${deliveryEstimate.minutesRemaining} min remaining)` : ''}
                                    </p>
                                ` : ''}
                            </div>
                            
                            <div class="order-actions">
                                <button type="button" class="btn btn-secondary btn-sm view-order-btn" data-order-id="${order.orderId}">View Details</button>
                                <button type="button" class="btn btn-primary btn-sm reorder-btn" data-order-id="${order.orderId}">Reorder</button>
                                ${order.status !== 'delivered' ? `
                                    <button type="button" class="btn btn-info btn-sm track-order-btn" data-order-id="${order.orderId}">Track Order</button>
                                ` : ''}
                            </div>
                        </div>
                    `;
                }).join('');

                // Add event listeners
                orderHistoryContainer.querySelectorAll('.view-order-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const orderId = e.target.dataset.orderId;
                        const trackingData = OrderTracking.getOrderTracking(orderId);
                        showOrderDetails(trackingData);
                    });
                });

                orderHistoryContainer.querySelectorAll('.reorder-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const orderId = e.target.dataset.orderId;
                        const trackingData = OrderTracking.getOrderTracking(orderId);
                        reorderItems(trackingData);
                    });
                });

                orderHistoryContainer.querySelectorAll('.track-order-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const orderId = e.target.dataset.orderId;
                        showOrderTracking(orderId);
                    });
                });
            } else {
                orderHistoryContainer.innerHTML = `
                    <div class="empty-state">
                        <p>You haven't placed any orders yet.</p>
                        <a href="menu.html" class="btn btn-primary">Browse Menu</a>
                    </div>
                `;
            }
        }

        function showOrderDetails(order) {
            const modal = document.getElementById('order-details-modal');
            const content = document.getElementById('order-details-content');
            
            const statusInfo = OrderTracking.ORDER_STATUSES.find(s => s.key === order.status);
            const deliveryEstimate = OrderTracking.getDeliveryEstimate(order);
            const progress = OrderTracking.getStatusProgress(order.status);

            content.innerHTML = `
                <div class="order-summary">
                    <div class="order-info">
                        <h4>Order #${order.orderId}</h4>
                        <p><strong>Tracking Number:</strong> ${order.trackingNumber}</p>
                        <p><strong>Date:</strong> ${Utils.formatDateTime(order.createdAt)}</p>
                        <p><strong>Status:</strong> <span class="order-status ${order.status}">${statusInfo?.label || order.status}</span></p>
                        ${deliveryEstimate ? `
                            <p><strong>${deliveryEstimate.type === 'delivered' ? 'Delivered:' : 'Estimated Delivery:'}:</strong> 
                            ${deliveryEstimate.formatted}
                            ${deliveryEstimate.minutesRemaining > 0 ? ` (${deliveryEstimate.minutesRemaining} min remaining)` : ''}
                            </p>
                        ` : ''}
                    </div>
                    
                    <div class="order-progress-section">
                        <h5>Order Progress</h5>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progress}%"></div>
                        </div>
                        <p class="progress-description">${statusInfo?.description || ''}</p>
                    </div>

                    <div class="order-items">
                        <h5>Items Ordered</h5>
                        ${order.items.map(item => `
                            <div class="order-item">
                                <div class="item-info">
                                    <span class="item-name">${item.name}</span>
                                    <span class="item-quantity">Qty: ${item.quantity}</span>
                                </div>
                                <span class="item-price">${Utils.formatCurrency(item.price * item.quantity)}</span>
                            </div>
                        `).join('')}
                    </div>

                    <div class="order-totals">
                        <div class="total-line">
                            <span>Subtotal:</span>
                            <span>${Utils.formatCurrency(order.totals.subtotal)}</span>
                        </div>
                        <div class="total-line">
                            <span>Delivery Fee:</span>
                            <span>${Utils.formatCurrency(order.totals.delivery)}</span>
                        </div>
                        <div class="total-line">
                            <span>Tax:</span>
                            <span>${Utils.formatCurrency(order.totals.tax)}</span>
                        </div>
                        <div class="total-line total">
                            <span><strong>Total:</strong></span>
                            <span><strong>${Utils.formatCurrency(order.totals.total)}</strong></span>
                        </div>
                    </div>

                    ${order.customerInfo ? `
                        <div class="delivery-info">
                            <h5>Delivery Information</h5>
                            <p><strong>Name:</strong> ${order.customerInfo.firstName} ${order.customerInfo.lastName}</p>
                            <p><strong>Phone:</strong> ${order.customerInfo.phone}</p>
                            <p><strong>Address:</strong> ${order.customerInfo.address}</p>
                            ${order.customerInfo.instructions ? `<p><strong>Instructions:</strong> ${order.customerInfo.instructions}</p>` : ''}
                        </div>
                    ` : ''}
                </div>
            `;

            modal.classList.remove('hidden');

            // Setup close button
            const closeBtn = document.getElementById('close-order-modal');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    modal.classList.add('hidden');
                });
            }

            // Close on backdrop click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.add('hidden');
                }
            });
        }

        function reorderItems(order) {
            if (typeof Cart !== 'undefined') {
                // Clear current cart
                Cart.clearCart();

                // Add items from the order
                order.items.forEach(item => {
                    for (let i = 0; i < item.quantity; i++) {
                        Cart.addItem({
                            id: item.id,
                            name: item.name,
                            price: item.price,
                            image: item.image,
                            category: item.category
                        });
                    }
                });

                MagicMenu.showToast(`${order.items.length} items added to cart!`, 'success');

                // Redirect to cart page after a short delay
                setTimeout(() => {
                    window.location.href = 'cart.html';
                }, 1000);
            } else {
                MagicMenu.showToast('Unable to add items to cart. Please try again.', 'error');
            }
        }

        function showOrderTracking(orderId) {
            const trackingData = OrderTracking.getOrderTracking(orderId);
            if (!trackingData) {
                MagicMenu.showToast('Order tracking data not found', 'error');
                return;
            }

            const modal = document.getElementById('order-details-modal');
            const content = document.getElementById('order-details-content');
            
            const statusInfo = OrderTracking.ORDER_STATUSES.find(s => s.key === trackingData.status);
            const deliveryEstimate = OrderTracking.getDeliveryEstimate(trackingData);
            const progress = OrderTracking.getStatusProgress(trackingData.status);
            const formattedTimeline = OrderTracking.formatTimeline(trackingData.timeline);

            content.innerHTML = `
                <div class="order-tracking">
                    <div class="tracking-header">
                        <h4>Order Tracking #${trackingData.orderId}</h4>
                        <p class="tracking-number">Tracking: ${trackingData.trackingNumber}</p>
                        <div class="current-status">
                            <span class="order-status ${trackingData.status}">${statusInfo?.label || trackingData.status}</span>
                            ${deliveryEstimate && deliveryEstimate.minutesRemaining > 0 ? `
                                <span class="delivery-countdown">${deliveryEstimate.minutesRemaining} min remaining</span>
                            ` : ''}
                        </div>
                    </div>
                    
                    <div class="tracking-progress">
                        <div class="progress-bar large">
                            <div class="progress-fill" style="width: ${progress}%"></div>
                        </div>
                        <div class="status-steps">
                            ${OrderTracking.ORDER_STATUSES.map((status, index) => {
                                const isCompleted = trackingData.timeline.some(t => t.status === status.key);
                                const isCurrent = trackingData.status === status.key;
                                return `
                                    <div class="status-step ${isCompleted ? 'completed' : ''} ${isCurrent ? 'current' : ''}">
                                        <div class="step-indicator"></div>
                                        <div class="step-label">${status.label}</div>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    </div>
                    
                    <div class="tracking-timeline">
                        <h5>Order Timeline</h5>
                        <div class="timeline">
                            ${formattedTimeline.reverse().map(entry => `
                                <div class="timeline-item">
                                    <div class="timeline-marker"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <span class="timeline-status">${OrderTracking.ORDER_STATUSES.find(s => s.key === entry.status)?.label || entry.status}</span>
                                            <span class="timeline-time">${entry.timeAgo}</span>
                                        </div>
                                        <p class="timeline-description">${entry.description}</p>
                                        ${entry.location ? `<p class="timeline-location">📍 ${entry.location}</p>` : ''}
                                        <p class="timeline-timestamp">${entry.formattedTime}</p>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    
                    ${deliveryEstimate ? `
                        <div class="delivery-estimate">
                            <h5>Delivery Information</h5>
                            <p><strong>${deliveryEstimate.type === 'delivered' ? 'Delivered:' : 'Estimated Delivery:'}:</strong> ${deliveryEstimate.formatted}</p>
                            ${trackingData.deliveryAddress ? `<p><strong>Address:</strong> ${trackingData.deliveryAddress}</p>` : ''}
                            ${trackingData.specialInstructions ? `<p><strong>Instructions:</strong> ${trackingData.specialInstructions}</p>` : ''}
                        </div>
                    ` : ''}
                </div>
            `;

            modal.classList.remove('hidden');

            // Setup close button
            const closeBtn = document.getElementById('close-order-modal');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    modal.classList.add('hidden');
                });
            }

            // Close on backdrop click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.add('hidden');
                }
            });
        }

        function setupAccountNavigation() {
            const navLinks = document.querySelectorAll('.account-nav-link');
            const sections = document.querySelectorAll('.account-section');
            
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    if (link.id === 'logout-btn') {
                        logout();
                        return;
                    }
                    
                    const sectionId = link.dataset.section;
                    
                    // Update active nav link
                    navLinks.forEach(l => l.classList.remove('active'));
                    link.classList.add('active');
                    
                    // Show corresponding section
                    sections.forEach(section => {
                        section.classList.remove('active');
                    });
                    document.getElementById(`${sectionId}-section`).classList.add('active');
                });
            });
        }

        function setupAuthTabs() {
            const authTabs = document.querySelectorAll('.auth-tab');
            const authForms = document.querySelectorAll('.auth-form');
            
            authTabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const tabType = tab.dataset.tab;
                    
                    // Update active tab
                    authTabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    
                    // Show corresponding form
                    authForms.forEach(form => {
                        form.classList.remove('active');
                    });
                    document.getElementById(`${tabType}-form`).classList.add('active');
                });
            });
        }

        function setupProfileEditing() {
            const editBtn = document.getElementById('edit-profile-btn');
            const cancelBtn = document.getElementById('cancel-edit-btn');
            const profileView = document.getElementById('profile-view');
            const profileEdit = document.getElementById('profile-edit');

            if (editBtn) {
                editBtn.addEventListener('click', () => {
                    const user = Utils.storage.get('user');
                    if (user) {
                        // Populate form with current data
                        const nameParts = user.name.split(' ');
                        document.getElementById('edit-firstName').value = nameParts[0] || '';
                        document.getElementById('edit-lastName').value = nameParts.slice(1).join(' ') || '';
                        document.getElementById('edit-email').value = user.email || '';
                        document.getElementById('edit-phone').value = user.phone || '';

                        // Show edit form
                        profileView.classList.add('hidden');
                        profileEdit.classList.remove('hidden');
                    }
                });
            }

            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => {
                    profileView.classList.remove('hidden');
                    profileEdit.classList.add('hidden');
                });
            }
        }

        function setupAddressManagement() {
            const addBtn = document.getElementById('add-address-btn');
            const modal = document.getElementById('address-modal');
            const closeBtn = document.getElementById('close-address-modal');
            const cancelBtn = document.getElementById('cancel-address-btn');

            if (addBtn) {
                addBtn.addEventListener('click', () => {
                    document.getElementById('address-modal-title').textContent = 'Add New Address';
                    document.querySelector('#address-modal form').reset();
                    document.querySelector('#address-modal form').removeAttribute('data-address-id');
                    modal.classList.remove('hidden');
                });
            }

            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    modal.classList.add('hidden');
                });
            }

            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => {
                    modal.classList.add('hidden');
                });
            }

            // Close modal on backdrop click
            modal?.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.add('hidden');
                }
            });
        }

        function loadAddresses() {
            try {
                const addresses = Utils.storage.get('userAddresses', []);
                const container = document.getElementById('address-list');

                if (!container) {
                    console.error('Address list container not found');
                    return;
                }

                if (addresses.length === 0) {
                    container.innerHTML = `
                        <div class="empty-addresses">
                            <p>You haven't saved any addresses yet.</p>
                            <p>Add your first address to make checkout faster.</p>
                        </div>
                    `;
                    return;
                }
            } catch (error) {
                console.error('Error loading addresses:', error);
                const container = document.getElementById('address-list');
                if (container) {
                    container.innerHTML = `
                        <div class="alert alert-error">
                            <p>Error loading addresses. Please refresh the page.</p>
                        </div>
                    `;
                }
                return;
            }

            container.innerHTML = addresses.map(address => `
                <div class="address-card ${address.isDefault ? 'default' : ''}" data-address-id="${address.id}">
                    <div class="address-card-header">
                        <div>
                            <div class="address-label">${address.label}</div>
                            ${address.isDefault ? '<span class="address-default-badge">Default</span>' : ''}
                        </div>
                    </div>
                    <div class="address-details">
                        <div>${address.street}</div>
                        <div>${address.city}, ${address.state}</div>
                        ${address.phone ? `<div>Phone: ${address.phone}</div>` : ''}
                        ${address.instructions ? `<div>Instructions: ${address.instructions}</div>` : ''}
                    </div>
                    <div class="address-actions">
                        <button type="button" class="btn btn-secondary edit-address-btn" data-address-id="${address.id}">Edit</button>
                        ${!address.isDefault ? `<button type="button" class="btn btn-secondary set-default-btn" data-address-id="${address.id}">Set Default</button>` : ''}
                        <button type="button" class="btn btn-danger delete-address-btn" data-address-id="${address.id}">Delete</button>
                    </div>
                </div>
            `).join('');

            // Add event listeners for address actions
            container.querySelectorAll('.edit-address-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const addressId = e.target.dataset.addressId;
                    editAddress(addressId);
                });
            });

            container.querySelectorAll('.set-default-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const addressId = e.target.dataset.addressId;
                    setDefaultAddress(addressId);
                });
            });

            container.querySelectorAll('.delete-address-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const addressId = e.target.dataset.addressId;
                    deleteAddress(addressId);
                });
            });
        }

        function editAddress(addressId) {
            const addresses = Utils.storage.get('userAddresses', []);
            const address = addresses.find(a => a.id === addressId);

            if (address) {
                document.getElementById('address-modal-title').textContent = 'Edit Address';
                document.getElementById('address-label').value = address.label;
                document.getElementById('address-street').value = address.street;
                document.getElementById('address-city').value = address.city;
                document.getElementById('address-state').value = address.state;
                document.getElementById('address-phone').value = address.phone || '';
                document.getElementById('address-instructions').value = address.instructions || '';
                document.getElementById('address-default').checked = address.isDefault;

                const form = document.querySelector('#address-modal form');
                form.setAttribute('data-address-id', addressId);

                document.getElementById('address-modal').classList.remove('hidden');
            }
        }

        function setDefaultAddress(addressId) {
            const addresses = Utils.storage.get('userAddresses', []);
            const updatedAddresses = addresses.map(address => ({
                ...address,
                isDefault: address.id === addressId
            }));

            Utils.storage.set('userAddresses', updatedAddresses);
            loadAddresses();
            MagicMenu.showToast('Default address updated successfully!', 'success');
        }

        function deleteAddress(addressId) {
            if (confirm('Are you sure you want to delete this address?')) {
                const addresses = Utils.storage.get('userAddresses', []);
                const updatedAddresses = addresses.filter(address => address.id !== addressId);

                Utils.storage.set('userAddresses', updatedAddresses);
                loadAddresses();
                MagicMenu.showToast('Address deleted successfully!', 'success');
            }
        }

        function setupPasswordReset() {
            const forgotLink = document.getElementById('forgot-password-link');
            const modal = document.getElementById('password-reset-modal');
            const closeBtn = document.getElementById('close-reset-modal');
            const cancelBtn = document.getElementById('cancel-reset-btn');
            const closeSuccessBtn = document.getElementById('close-reset-success');
            const resendLink = document.getElementById('resend-reset-link');

            if (forgotLink) {
                forgotLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    document.getElementById('reset-step-1').classList.remove('hidden');
                    document.getElementById('reset-step-2').classList.add('hidden');
                    document.querySelector('#password-reset-modal form').reset();
                    modal.classList.remove('hidden');
                });
            }

            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    modal.classList.add('hidden');
                });
            }

            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => {
                    modal.classList.add('hidden');
                });
            }

            if (closeSuccessBtn) {
                closeSuccessBtn.addEventListener('click', () => {
                    modal.classList.add('hidden');
                });
            }

            if (resendLink) {
                resendLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    MagicMenu.showToast('Reset link sent again!', 'info');
                });
            }

            // Close modal on backdrop click
            modal?.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.add('hidden');
                }
            });
        }

        function setupPreferences() {
            // Initialize notification preferences system
            if (typeof NotificationPreferences !== 'undefined') {
                NotificationPreferences.init();
            }

            const saveBtn = document.getElementById('save-preferences-btn');
            const resetBtn = document.getElementById('reset-preferences-btn');

            if (saveBtn) {
                saveBtn.addEventListener('click', () => {
                    // Manual save trigger (preferences are auto-saved on change)
                    if (typeof NotificationPreferences !== 'undefined') {
                        const preferences = NotificationPreferences.getPreferences();
                        const result = NotificationPreferences.savePreferences(preferences);
                        
                        if (result.success) {
                            NotificationPreferences.showPreferenceStatus('All preferences saved successfully!', 'success');
                        } else {
                            NotificationPreferences.showPreferenceStatus('Failed to save some preferences', 'error');
                        }
                    } else {
                        // Fallback for when NotificationPreferences is not available
                        MagicMenu.showToast('Preferences saved!', 'success');
                    }
                });
            }

            if (resetBtn) {
                resetBtn.addEventListener('click', () => {
                    if (typeof NotificationPreferences !== 'undefined') {
                        NotificationPreferences.resetToDefaults();
                    }
                });
            }
        }

        function loadPreferences() {
            if (typeof NotificationPreferences !== 'undefined') {
                // Load preferences using the new system
                NotificationPreferences.loadPreferences();
            } else {
                // Fallback for backward compatibility
                const preferences = Utils.storage.get('userPreferences', {
                    'email-notifications': true,
                    'sms-notifications': true,
                    'newsletter': false
                });

                // Apply basic preferences
                Object.keys(preferences).forEach(key => {
                    const element = document.querySelector(`input[name="${key}"]`);
                    if (element && element.type === 'checkbox') {
                        element.checked = preferences[key];
                    }
                });
            }
        }

        function setupAccountSettings() {
            const changePasswordBtn = document.getElementById('change-password-btn');
            const downloadDataBtn = document.getElementById('download-data-btn');

            if (changePasswordBtn) {
                changePasswordBtn.addEventListener('click', () => {
                    MagicMenu.showToast('Password change functionality would be implemented here.', 'info');
                });
            }

            if (downloadDataBtn) {
                downloadDataBtn.addEventListener('click', () => {
                    const result = AccountDeletion.downloadUserData();
                    if (result.success) {
                        MagicMenu.showToast(result.message, 'success');
                    } else {
                        MagicMenu.showToast(result.error, 'error');
                    }
                });
            }
        }

        // Setup account deletion functionality
        function setupAccountDeletion() {
            const deleteAccountBtn = document.getElementById('delete-account-btn');
            const deletionModal = document.getElementById('account-deletion-modal');
            const closeDeletionModal = document.getElementById('close-deletion-modal');
            
            let deletionSessionId = null;

            // Open deletion modal
            if (deleteAccountBtn) {
                deleteAccountBtn.addEventListener('click', () => {
                    const result = AccountDeletion.initiateAccountDeletion();
                    if (result.success) {
                        deletionSessionId = result.sessionId;
                        showDeletionStep(1);
                        deletionModal.classList.remove('hidden');
                    } else {
                        MagicMenu.showToast(result.error, 'error');
                    }
                });
            }

            // Close modal
            if (closeDeletionModal) {
                closeDeletionModal.addEventListener('click', () => {
                    closeDeletionModalHandler();
                });
            }

            // Close modal on backdrop click
            deletionModal?.addEventListener('click', (e) => {
                if (e.target === deletionModal) {
                    closeDeletionModalHandler();
                }
            });

            // Step 1 handlers
            const proceedBtn = document.getElementById('proceed-deletion-btn');
            const cancelBtn = document.getElementById('cancel-deletion-btn');
            const exportCheckbox = document.getElementById('export-data-checkbox');

            if (proceedBtn) {
                proceedBtn.addEventListener('click', () => {
                    // Export data if requested
                    if (exportCheckbox && exportCheckbox.checked) {
                        const result = AccountDeletion.downloadUserData();
                        if (result.success) {
                            MagicMenu.showToast('Data export downloaded', 'success');
                        } else {
                            MagicMenu.showToast('Failed to export data: ' + result.error, 'error');
                            return;
                        }
                    }
                    showDeletionStep(2);
                });
            }

            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => {
                    closeDeletionModalHandler();
                });
            }

            // Step 2 handlers
            const passwordForm = document.getElementById('deletion-password-form');
            const backToStep1Btn = document.getElementById('back-to-step1-btn');

            if (passwordForm) {
                passwordForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    const password = document.getElementById('deletion-password').value;
                    
                    const result = AccountDeletion.verifyPasswordForDeletion(password, deletionSessionId);
                    if (result.success) {
                        showDeletionStep(3);
                    } else {
                        MagicMenu.showToast(result.error, 'error');
                    }
                });
            }

            if (backToStep1Btn) {
                backToStep1Btn.addEventListener('click', () => {
                    showDeletionStep(1);
                });
            }

            // Step 3 handlers
            const finalConfirmationInput = document.getElementById('final-confirmation-input');
            const finalDeleteBtn = document.getElementById('final-delete-btn');
            const cancelFinalBtn = document.getElementById('cancel-final-btn');

            if (finalConfirmationInput) {
                finalConfirmationInput.addEventListener('input', (e) => {
                    const isValid = e.target.value.toUpperCase() === 'DELETE';
                    finalDeleteBtn.disabled = !isValid;
                });
            }

            if (finalDeleteBtn) {
                finalDeleteBtn.addEventListener('click', () => {
                    const confirmation = finalConfirmationInput.value.toUpperCase() === 'DELETE';
                    
                    if (!confirmation) {
                        MagicMenu.showToast('Please type "DELETE" to confirm', 'error');
                        return;
                    }

                    // Show loading state
                    finalDeleteBtn.disabled = true;
                    finalDeleteBtn.textContent = 'Deleting...';

                    // Execute deletion after short delay
                    setTimeout(() => {
                        const result = AccountDeletion.executeAccountDeletion(deletionSessionId, true);
                        if (result.success) {
                            showDeletionStep(4);
                        } else {
                            MagicMenu.showToast(result.error, 'error');
                            finalDeleteBtn.disabled = false;
                            finalDeleteBtn.textContent = 'Delete My Account';
                        }
                    }, 2000);
                });
            }

            if (cancelFinalBtn) {
                cancelFinalBtn.addEventListener('click', () => {
                    closeDeletionModalHandler();
                });
            }

            // Step 4 handler
            const deletionCompleteBtn = document.getElementById('deletion-complete-btn');
            if (deletionCompleteBtn) {
                deletionCompleteBtn.addEventListener('click', () => {
                    window.location.href = 'index.html';
                });
            }

            function showDeletionStep(step) {
                // Hide all steps
                for (let i = 1; i <= 4; i++) {
                    const stepElement = document.getElementById(`deletion-step-${i}`);
                    if (stepElement) {
                        stepElement.classList.add('hidden');
                    }
                }

                // Show current step
                const currentStep = document.getElementById(`deletion-step-${step}`);
                if (currentStep) {
                    currentStep.classList.remove('hidden');
                }

                // Reset forms when going back
                if (step === 1) {
                    const exportCheckbox = document.getElementById('export-data-checkbox');
                    if (exportCheckbox) exportCheckbox.checked = false;
                } else if (step === 2) {
                    const passwordForm = document.getElementById('deletion-password-form');
                    if (passwordForm) passwordForm.reset();
                } else if (step === 3) {
                    const finalInput = document.getElementById('final-confirmation-input');
                    const finalBtn = document.getElementById('final-delete-btn');
                    if (finalInput) finalInput.value = '';
                    if (finalBtn) {
                        finalBtn.disabled = true;
                        finalBtn.textContent = 'Delete My Account';
                    }
                }
            }

            function closeDeletionModalHandler() {
                if (deletionSessionId) {
                    AccountDeletion.cancelDeletion(deletionSessionId);
                    deletionSessionId = null;
                }
                deletionModal.classList.add('hidden');
                showDeletionStep(1);
            }
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                Utils.storage.remove('user');
                MagicMenu.showToast('You have been logged out successfully.', 'info');
                checkAuthStatus();
            }
        }

        // Initialize the page when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                PageInitializer.init();
            });
        } else {
            // DOM is already ready
            PageInitializer.init();
        }
    </script>
</body>
</html>
