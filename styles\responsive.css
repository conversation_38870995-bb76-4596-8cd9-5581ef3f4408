/* Magic Menu - Responsive Styles */

/* Mobile First Approach - Base styles are for mobile */

/* Small Mobile (320px and up) - Base styles already applied */

/* Medium Mobile and up (480px) */
@media (min-width: 480px) {
    .container {
        padding: 0 var(--spacing-lg);
    }
    
    .card-img-top {
        height: 220px;
    }
    
    .btn-lg {
        padding: var(--spacing-lg) var(--spacing-2xl);
    }
}

/* Tablet and up (768px) */
@media (min-width: 768px) {
    /* Typography adjustments */
    h1 {
        font-size: 3rem; /* 48px */
    }
    
    h2 {
        font-size: 2.25rem; /* 36px */
    }
    
    .container {
        padding: 0 var(--spacing-xl);
    }
    
    .section {
        padding: var(--spacing-3xl) 0;
    }
    
    /* Navigation */
    .navbar-nav {
        gap: var(--spacing-xl);
    }
    
    /* Grid layouts */
    .grid-2 {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
    
    .grid-3 {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
    
    /* Cards */
    .card-img-top {
        height: 240px;
    }
    
    /* Forms */
    .form-row {
        display: flex;
        gap: var(--spacing-lg);
    }
    
    .form-row .form-group {
        flex: 1;
    }
    
    /* Modal */
    .modal-dialog {
        max-width: 600px;
    }
}

/* Desktop and up (1024px) */
@media (min-width: 1024px) {
    /* Typography */
    h1 {
        font-size: var(--font-size-4xl); /* 40px */
    }
    
    .container {
        max-width: 1200px;
    }
    
    /* Navigation - Show full menu */
    .navbar-toggle {
        display: none;
    }
    
    .navbar-nav {
        display: flex !important;
        flex-direction: row;
        gap: var(--spacing-2xl);
    }
    
    /* Grid layouts */
    .grid-3 {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .grid-4 {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-lg);
    }
    
    /* Hero sections */
    .hero {
        min-height: 60vh;
        display: flex;
        align-items: center;
    }
    
    .hero-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-3xl);
        align-items: center;
    }
    
    /* Cards */
    .card-img-top {
        height: 260px;
    }
    
    /* Two-column layouts */
    .two-column {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-3xl);
        align-items: start;
    }
    
    /* Sidebar layouts */
    .sidebar-layout {
        display: grid;
        grid-template-columns: 300px 1fr;
        gap: var(--spacing-2xl);
    }
}

/* Large Desktop and up (1200px) */
@media (min-width: 1200px) {
    .container {
        max-width: 1400px;
    }
    
    /* Grid layouts */
    .grid-5 {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: var(--spacing-lg);
    }
    
    .grid-6 {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: var(--spacing-lg);
    }
    
    /* Hero sections */
    .hero {
        min-height: 70vh;
    }
    
    /* Cards */
    .card-img-top {
        height: 280px;
    }
}

/* Mobile Navigation (up to 1023px) */
@media (max-width: 1023px) {
    .navbar-toggle {
        display: block;
    }
    
    .navbar-nav {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--white);
        box-shadow: var(--shadow-lg);
        flex-direction: column;
        padding: var(--spacing-lg);
        gap: var(--spacing-sm);
    }
    
    .navbar-nav.show {
        display: flex;
    }
    
    .navbar-nav a {
        padding: var(--spacing-md);
        border-radius: var(--border-radius-md);
    }
}

/* Print styles */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    a,
    a:visited {
        text-decoration: underline;
    }
    
    .navbar,
    .btn,
    .modal,
    .alert {
        display: none !important;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-light: #000000;
        --text-muted: #000000;
    }
    
    .card {
        border: 2px solid var(--border-color);
    }
    
    .btn {
        border-width: 2px;
    }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles would go here */
    /* Currently not implemented as per requirements */
}

/* Utility classes for responsive design */
.d-none {
    display: none;
}

.d-block {
    display: block;
}

.d-flex {
    display: flex;
}

.d-grid {
    display: grid;
}

/* Mobile utilities */
@media (max-width: 767px) {
    .d-md-none {
        display: none;
    }
    
    .d-md-block {
        display: block;
    }
    
    .text-center-mobile {
        text-align: center;
    }
    
    .btn-block-mobile {
        width: 100%;
        display: flex;
    }
}

/* Tablet utilities */
@media (min-width: 768px) {
    .d-md-none {
        display: none;
    }
    
    .d-md-block {
        display: block;
    }
    
    .d-md-flex {
        display: flex;
    }
    
    .d-md-grid {
        display: grid;
    }
}

/* Desktop utilities */
@media (min-width: 1024px) {
    .d-lg-none {
        display: none;
    }
    
    .d-lg-block {
        display: block;
    }
    
    .d-lg-flex {
        display: flex;
    }
    
    .d-lg-grid {
        display: grid;
    }
}
