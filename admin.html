<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Magic Menu Admin Dashboard - Manage menu items, orders, and restaurant operations.">
    <meta name="keywords" content="admin dashboard, restaurant management, Magic Menu admin">
    <meta name="robots" content="noindex, nofollow">
    <title>Admin Dashboard - Magic Menu | Restaurant Management</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/icons/favicon.ico">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="styles/base.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/pages.css">
    <link rel="stylesheet" href="styles/responsive.css">
</head>
<body>
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Admin Header -->
    <header class="admin-header">
        <div class="container">
            <h1>Magic Menu Admin Dashboard</h1>
            <p>Restaurant Management System</p>
        </div>
    </header>

    <!-- Admin Navigation -->
    <nav class="admin-nav">
        <div class="container">
            <div class="admin-nav-container">
                <a href="#dashboard" class="admin-nav-link active" data-section="dashboard">Dashboard</a>
                <a href="#orders" class="admin-nav-link" data-section="orders">Orders</a>
                <a href="#menu" class="admin-nav-link" data-section="menu">Menu Management</a>
                <a href="#customers" class="admin-nav-link" data-section="customers">Customers</a>
                <a href="#analytics" class="admin-nav-link" data-section="analytics">Analytics</a>
                <a href="index.html" class="admin-nav-link">Back to Website</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
        <div class="container">
            <!-- Dashboard Section -->
            <section id="dashboard-section" class="admin-section active">
                <h2>Dashboard Overview</h2>
                
                <div class="admin-stats">
                    <div class="stat-card">
                        <div class="stat-number">127</div>
                        <div class="stat-label">Total Orders Today</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number">₦45,230</div>
                        <div class="stat-label">Revenue Today</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number">8</div>
                        <div class="stat-label">Pending Orders</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number">342</div>
                        <div class="stat-label">Total Customers</div>
                    </div>
                </div>

                <div class="dashboard-content">
                    <div class="recent-orders">
                        <h3>Recent Orders</h3>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Items</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>#MM001234</td>
                                    <td>John Doe</td>
                                    <td>Jollof Rice, Grilled Chicken</td>
                                    <td>₦5,500</td>
                                    <td><span class="badge badge-warning">Preparing</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-primary">View</button>
                                            <button class="btn btn-sm btn-success">Update</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>#MM001235</td>
                                    <td>Jane Smith</td>
                                    <td>Egusi Soup, Pounded Yam</td>
                                    <td>₦4,200</td>
                                    <td><span class="badge badge-primary">Out for Delivery</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-primary">View</button>
                                            <button class="btn btn-sm btn-success">Update</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>#MM001236</td>
                                    <td>Mike Johnson</td>
                                    <td>Suya, Chapman</td>
                                    <td>₦3,000</td>
                                    <td><span class="badge badge-success">Delivered</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-primary">View</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Orders Section -->
            <section id="orders-section" class="admin-section">
                <h2>Order Management</h2>
                
                <div class="section-controls">
                    <div class="filter-controls">
                        <select class="form-control">
                            <option value="all">All Orders</option>
                            <option value="pending">Pending</option>
                            <option value="preparing">Preparing</option>
                            <option value="delivery">Out for Delivery</option>
                            <option value="delivered">Delivered</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                        
                        <input type="date" class="form-control" value="2024-01-15">
                        
                        <button class="btn btn-primary">Filter</button>
                    </div>
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Date/Time</th>
                            <th>Customer</th>
                            <th>Items</th>
                            <th>Total</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>#MM001234</td>
                            <td>2024-01-15 14:30</td>
                            <td>John Doe<br><small>+234 ************</small></td>
                            <td>Jollof Rice (2), Grilled Chicken (1)</td>
                            <td>₦5,500</td>
                            <td>
                                <select class="form-control">
                                    <option value="pending">Pending</option>
                                    <option value="preparing" selected>Preparing</option>
                                    <option value="delivery">Out for Delivery</option>
                                    <option value="delivered">Delivered</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-sm btn-primary">View Details</button>
                                    <button class="btn btn-sm btn-success">Update Status</button>
                                </div>
                            </td>
                        </tr>
                        <!-- More orders would be loaded here -->
                    </tbody>
                </table>
            </section>

            <!-- Menu Management Section -->
            <section id="menu-section" class="admin-section">
                <h2>Menu Management</h2>
                
                <div class="section-controls">
                    <button class="btn btn-primary">Add New Item</button>
                    <button class="btn btn-secondary">Import Menu</button>
                    <button class="btn btn-secondary">Export Menu</button>
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Category</th>
                            <th>Price</th>
                            <th>Status</th>
                            <th>Popular</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><img src="images/menu/jollof-rice.jpg" alt="Jollof Rice" style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px;"></td>
                            <td>Jollof Rice</td>
                            <td>Rice Dishes</td>
                            <td>₦2,500</td>
                            <td><span class="badge badge-success">Available</span></td>
                            <td><span class="badge badge-primary">Popular</span></td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-sm btn-primary">Edit</button>
                                    <button class="btn btn-sm btn-secondary">Disable</button>
                                    <button class="btn btn-sm btn-secondary">Delete</button>
                                </div>
                            </td>
                        </tr>
                        <!-- More menu items would be loaded here -->
                    </tbody>
                </table>
            </section>

            <!-- Customers Section -->
            <section id="customers-section" class="admin-section">
                <h2>Customer Management</h2>
                
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Customer ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Total Orders</th>
                            <th>Total Spent</th>
                            <th>Last Order</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>#C001</td>
                            <td>John Doe</td>
                            <td><EMAIL></td>
                            <td>+234 ************</td>
                            <td>15</td>
                            <td>₦67,500</td>
                            <td>2024-01-15</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-sm btn-primary">View Profile</button>
                                    <button class="btn btn-sm btn-secondary">Send Message</button>
                                </div>
                            </td>
                        </tr>
                        <!-- More customers would be loaded here -->
                    </tbody>
                </table>
            </section>

            <!-- Analytics Section -->
            <section id="analytics-section" class="admin-section">
                <h2>Analytics & Reports</h2>
                
                <div class="analytics-content">
                    <div class="analytics-summary">
                        <h3>Sales Summary</h3>
                        <div class="admin-stats">
                            <div class="stat-card">
                                <div class="stat-number">₦1,234,567</div>
                                <div class="stat-label">Total Revenue (This Month)</div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-number">2,847</div>
                                <div class="stat-label">Total Orders (This Month)</div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-number">₦434</div>
                                <div class="stat-label">Average Order Value</div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-number">89%</div>
                                <div class="stat-label">Customer Satisfaction</div>
                            </div>
                        </div>
                    </div>

                    <div class="popular-items">
                        <h3>Most Popular Items</h3>
                        <ol>
                            <li>Jollof Rice - 234 orders</li>
                            <li>Egusi Soup - 189 orders</li>
                            <li>Grilled Chicken - 156 orders</li>
                            <li>Fried Rice - 134 orders</li>
                            <li>Suya - 98 orders</li>
                        </ol>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/utils.js"></script>
    <script>
        // Admin dashboard functionality
        document.addEventListener('DOMContentLoaded', () => {
            setupAdminNavigation();
            
            // Simple authentication check (in production, use proper authentication)
            const adminPassword = prompt('Enter admin password:');
            if (adminPassword !== 'admin123') {
                alert('Access denied. Redirecting to homepage.');
                window.location.href = 'index.html';
            }
        });

        function setupAdminNavigation() {
            const navLinks = document.querySelectorAll('.admin-nav-link');
            const sections = document.querySelectorAll('.admin-section');
            
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    const sectionId = link.dataset.section;
                    if (!sectionId) return;
                    
                    // Update active nav link
                    navLinks.forEach(l => l.classList.remove('active'));
                    link.classList.add('active');
                    
                    // Show corresponding section
                    sections.forEach(section => {
                        section.classList.remove('active');
                    });
                    document.getElementById(`${sectionId}-section`).classList.add('active');
                });
            });
        }
    </script>
</body>
</html>
